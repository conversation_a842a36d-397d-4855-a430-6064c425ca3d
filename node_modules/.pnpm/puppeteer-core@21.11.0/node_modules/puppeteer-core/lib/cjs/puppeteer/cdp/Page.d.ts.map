{"version": 3, "file": "Page.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/Page.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,QAAQ,CAAC;AAErC,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAGhD,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAC;AAC/C,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAkB,KAAK,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAEtE,OAAO,KAAK,EAAC,KAAK,EAAE,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAE3D,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACzD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,EACL,IAAI,EAEJ,KAAK,kBAAkB,EACvB,KAAK,YAAY,EACjB,KAAK,OAAO,EACZ,KAAK,2BAA2B,EAEhC,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACxB,MAAM,gBAAgB,CAAC;AAMxB,OAAO,EAAC,WAAW,EAAC,MAAM,0BAA0B,CAAC;AAErD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAC;AAWxD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAMpD,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAIjD,OAAO,EAAC,QAAQ,EAAC,MAAM,eAAe,CAAC;AACvC,OAAO,KAAK,EAAC,mBAAmB,EAAC,MAAM,0BAA0B,CAAC;AAKlE,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,YAAY,CAAC;AAGzC,OAAO,EAAC,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAC,MAAM,YAAY,CAAC;AAGjE,OAAO,KAAK,EAAC,WAAW,EAAE,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AACxE,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,aAAa,CAAC;AAG3C,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAMrC,OAAO,EAAC,YAAY,EAAC,MAAM,gBAAgB,CAAC;AAE5C;;GAEG;AACH,qBAAa,OAAQ,SAAQ,IAAI;;WAClB,OAAO,CAClB,MAAM,EAAE,UAAU,EAClB,MAAM,EAAE,SAAS,EACjB,iBAAiB,EAAE,OAAO,EAC1B,eAAe,EAAE,QAAQ,GAAG,IAAI,GAC/B,OAAO,CAAC,OAAO,CAAC;gBA+HjB,MAAM,EAAE,UAAU,EAClB,MAAM,EAAE,SAAS,EACjB,iBAAiB,EAAE,OAAO;IA+K5B,OAAO,IAAI,UAAU;IAIZ,uBAAuB,IAAI,OAAO;IAIlC,yBAAyB,IAAI,OAAO;IAIpC,mBAAmB,IAAI,OAAO;IAIxB,kBAAkB,CAC/B,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,WAAW,CAAC;IA6BR,cAAc,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IAIhE,MAAM,IAAI,SAAS;IAInB,OAAO,IAAI,OAAO;IAIlB,cAAc,IAAI,cAAc;IAuBhC,SAAS,IAAI,QAAQ;IAI9B,IAAa,QAAQ,IAAI,WAAW,CAEnC;IAED,IAAa,WAAW,IAAI,cAAc,CAEzC;IAED,IAAa,QAAQ,IAAI,QAAQ,CAEhC;IAED,IAAa,OAAO,IAAI,OAAO,CAE9B;IAED,IAAa,aAAa,IAAI,aAAa,CAE1C;IAEQ,MAAM,IAAI,KAAK,EAAE;IAIjB,OAAO,IAAI,YAAY,EAAE;IAInB,sBAAsB,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAMrD,sBAAsB,CAAC,MAAM,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAQtD,mBAAmB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAOpD,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAI/C,wBAAwB,CACrC,iBAAiB,EAAE,iBAAiB,GAAG,IAAI,GAC1C,OAAO,CAAC,IAAI,CAAC;IAMP,2BAA2B,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIlD,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIxC,iBAAiB,IAAI,MAAM;IAIrB,YAAY,CAAC,SAAS,EACnC,eAAe,EAAE,QAAQ,CAAC,SAAS,CAAC,GACnC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;IAkBlB,OAAO,CACpB,GAAG,IAAI,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;IAmBtB,YAAY,CACzB,GAAG,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,GAClD,OAAO,CAAC,IAAI,CAAC;IAWD,SAAS,CACtB,GAAG,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,GACzC,OAAO,CAAC,IAAI,CAAC;IA0BD,cAAc,CAC3B,IAAI,EAAE,MAAM,EACZ,YAAY,EAAE,QAAQ,GAAG;QAAC,OAAO,EAAE,QAAQ,CAAA;KAAC,GAC3C,OAAO,CAAC,IAAI,CAAC;IAmDD,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAgClD,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAIrD,mBAAmB,CAChC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAC9B,OAAO,CAAC,IAAI,CAAC;IAID,YAAY,CACzB,SAAS,EAAE,MAAM,EACjB,iBAAiB,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GACvD,OAAO,CAAC,IAAI,CAAC;IAOD,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IAyJ3B,MAAM,CACnB,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAShB,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC;IAIvC,MAAM,CACnB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAIhB,SAAS,CACtB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAwBhB,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7B,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAIrD,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7C,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1D,oBAAoB,CACjC,QAAQ,CAAC,EAAE,YAAY,EAAE,GACxB,OAAO,CAAC,IAAI,CAAC;IAID,eAAe,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAInD,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC1C,YAAY,EAAE,OAAO,CAAC;QACtB,gBAAgB,EAAE,OAAO,CAAC;KAC3B,GAAG,OAAO,CAAC,IAAI,CAAC;IAIF,uBAAuB,CACpC,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,kCAAkC,CAAC,MAAM,CAAC,GACnE,OAAO,CAAC,IAAI,CAAC;IAID,WAAW,CAAC,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAQpD,QAAQ,IAAI,QAAQ,GAAG,IAAI;IAIrB,qBAAqB,CAClC,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,EAExE,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,2BAA2B,CAAC;IAYxB,mCAAmC,CAChD,UAAU,EAAE,MAAM,GACjB,OAAO,CAAC,IAAI,CAAC;IASD,eAAe,CAAC,OAAO,UAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C,WAAW,CACxB,OAAO,EAAE,QAAQ,CAAC,iBAAiB,CAAC,GACnC,OAAO,CAAC,MAAM,CAAC;IAwDH,eAAe,CAAC,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;IA6D5D,GAAG,CAAC,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,MAAM,CAAC;IAQ9C,KAAK,CAClB,OAAO,GAAE;QAAC,eAAe,CAAC,EAAE,OAAO,CAAA;KAAgC,GAClE,OAAO,CAAC,IAAI,CAAC;IAiBP,QAAQ,IAAI,OAAO;IAI5B,IAAa,KAAK,IAAI,QAAQ,CAE7B;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACY,mBAAmB,CAChC,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,mBAAmB,CAAC;CAGhC"}