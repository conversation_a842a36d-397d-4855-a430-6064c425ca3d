{"version": 3, "file": "ExposedFunction.js", "sourceRoot": "", "sources": ["../../../../src/bidi/ExposedFunction.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iFAAmE;AAGnE,+CAA6C;AAC7C,iDAAyC;AACzC,qDAA6C;AAC7C,qDAA2E;AAG3E,uDAAmD;AAEnD,mDAA+C;AAe/C;;GAEG;AACH,MAAa,kBAAkB;IACpB,MAAM,CAAC;IAEP,IAAI,CAAC;IACL,MAAM,CAAC;IAEP,SAAS,CAAC;IACV,YAAY,GAAG,IAAI,GAAG,EAG5B,CAAC;IAEJ,gBAAgB,CAA6B;IAE7C,YACE,KAAgB,EAChB,IAAY,EACZ,KAAwC;QAExC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG;YACf,IAAI,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,wBAAwB,IAAI,CAAC,IAAI,OAAO;YAC7E,OAAO,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,wBAAwB,IAAI,CAAC,IAAI,UAAU;YACnF,MAAM,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,wBAAwB,IAAI,CAAC,IAAI,SAAS;SAClF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM;QACV,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEhD,8DAA8D;QAC9D,UAAU,CAAC,EAAE,CACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAC3C,IAAI,CAAC,uBAAuB,CAC7B,CAAC;QACF,UAAU,CAAC,EAAE,CACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAC3C,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,UAAU,CAAC,EAAE,CACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAC3C,IAAI,CAAC,oBAAoB,CAC1B,CAAC;QAEF,MAAM,mBAAmB,GAAG,IAAA,+BAAiB,EAC3C,IAAA,iCAAmB,EACjB,CACE,QAA+B,EAC/B,WAAoC,EACpC,UAA6B,EAC7B,EAAE;YACF,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBACxB,CAAC,WAAW,CAAC,MAAM,CAAW,CAAC,EAAE,UAAU,GAAG,IAAU;oBACtD,OAAO,IAAI,OAAO,CAChB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAClB,QAAQ,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;wBACrB,WAAW,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;wBAC3B,UAAU,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;wBACzB,EAAE,EAAE,CAAC;oBACP,CAAC,CACF,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAClC,CACF,CAAC;QAEF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAChE,mBAAmB;YACnB,SAAS,EAAE,gBAAgB;YAC3B,QAAQ,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC;SAC/C,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;QAEtC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM;aACR,IAAI,EAAE;aACN,MAAM,EAAE;aACR,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YACjB,OAAO,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAClD,mBAAmB;gBACnB,SAAS,EAAE,gBAAgB;gBAC3B,YAAY,EAAE,KAAK;gBACnB,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,MAAM;aACvC,CAAC,CAAC;QACL,CAAC,CAAC,CACL,CAAC;IACJ,CAAC;IAED,uBAAuB,GAAG,KAAK,EAAE,MAAqC,EAAE,EAAE;QACxE,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAC3C,OAAO;QACT,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,EAAC,SAAS,EAAE,WAAW,EAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,IAAA,kBAAM,EAAC,IAAI,CAAC,CAAC;QACb,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,kCAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;YACxE,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC3C,mBAAmB,EAAE,IAAA,+BAAiB,EAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAM,EAAE,MAAM,EAAE,EAAE;oBACnE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CAAC;gBACF,SAAS,EAAE;oBACT,CAAC,MAAM,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,CAA2B;oBAClE,8BAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC;iBAC5C;gBACD,YAAY,EAAE,KAAK;gBACnB,MAAM,EAAE;oBACN,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;iBAC3B;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC;gBACH,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBAC3C,mBAAmB,EAAE,IAAA,+BAAiB,EACpC,CACE,CAAC,CAAC,EAAE,MAAM,CAAoC,EAC9C,IAAY,EACZ,OAAe,EACf,KAAc,EACd,EAAE;4BACF,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;4BACjC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;4BAClB,IAAI,KAAK,EAAE,CAAC;gCACV,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;4BACtB,CAAC;4BACD,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC,CACF;wBACD,SAAS,EAAE;4BACT,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAA2B;4BACjE,8BAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC;4BAC/C,8BAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC;4BAClD,8BAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC;yBACjD;wBACD,YAAY,EAAE,KAAK;wBACnB,MAAM,EAAE;4BACN,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;yBAC3B;qBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBAC3C,mBAAmB,EAAE,IAAA,+BAAiB,EACpC,CACE,CAAC,CAAC,EAAE,MAAM,CAAsC,EAChD,KAAc,EACd,EAAE;4BACF,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC,CACF;wBACD,SAAS,EAAE;4BACT,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAA2B;4BACjE,8BAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC;yBAC3C;wBACD,YAAY,EAAE,KAAK;wBACnB,MAAM,EAAE;4BACN,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;yBAC3B;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAC1C,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO;YACL;gBACE,IAAI,EAAE,SAAkB;gBACxB,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;oBAC5B,SAAS,+CAAkC;iBAC5C;aACF;YACD;gBACE,IAAI,EAAE,SAAkB;gBACxB,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;oBAC/B,SAAS,+CAAkC;iBAC5C;aACF;YACD;gBACE,IAAI,EAAE,SAAkB;gBACxB,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;oBAC9B,SAAS,+CAAkC;iBAC5C;aACF;SACF,CAAC;IACJ,CAAC;IAED,qBAAqB,GAAG,CAAC,MAAqC,EAAE,EAAE;QAChE,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC9C,OAAO;QACT,CAAC;QACD,MAAM,EAAC,SAAS,EAAE,WAAW,EAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAC1E,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACzC,CAAC,CAAC;IAEF,oBAAoB,GAAG,CAAC,MAAqC,EAAE,EAAE;QAC/D,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC7C,OAAO;QACT,CAAC;QACD,MAAM,EAAC,SAAS,EAAE,WAAW,EAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAC1E,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,2BAA2B,CAAC,MAAqC;QAC/D,MAAM,EAAC,IAAI,EAAE,MAAM,EAAC,GAAG,MAAM,CAAC;QAC9B,IAAA,kBAAM,EAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;QAC9B,IAAA,kBAAM,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,IAAA,kBAAM,EAAC,cAAc,CAAC,CAAC;QACvB,IAAA,kBAAM,EAAC,cAAc,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QACzC,IAAA,kBAAM,EAAC,OAAO,cAAc,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;QAEjD,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;QACtC,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,SAAS,GAAG;gBACV,OAAO,EAAE,IAAI,sBAAQ,EAAE;gBACvB,MAAM,EAAE,IAAI,sBAAQ,EAAE;aACvB,CAAC;YACF,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,EAAC,SAAS,EAAE,WAAW,EAAE,IAAI,EAAC,CAAC;IACxC,CAAC;IAED,CAAC,MAAM,CAAC,OAAO,CAAC;QACd,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;QACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,gBAAgB;aAC9B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AAnQD,gDAmQC"}