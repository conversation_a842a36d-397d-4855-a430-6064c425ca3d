{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/bidi/HTTPRequest.ts"], "names": [], "mappings": ";;;AAYA,0DAAqE;AACrE,mDAAyD;AAIzD;;GAEG;AACH,MAAa,eAAgB,SAAQ,4BAAW;IACrC,SAAS,GAA4B,IAAI,CAAC;IAC1C,cAAc,CAAoB;IAC3C,aAAa,CAAgB;IAE7B,IAAI,CAAS;IACb,aAAa,CAAe;IAE5B,OAAO,CAAS;IAChB,SAAS,CAAU;IACnB,QAAQ,GAA2B,EAAE,CAAC;IACtC,UAAU,CAAyB;IACnC,MAAM,CAAe;IAErB,YACE,KAA+C,EAC/C,KAAmB,EACnB,gBAAmC,EAAE;QAErC,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAkB,CAAC;QACxE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC;QAEtC,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC3C,qCAAqC;YACrC,4DAA4D;YAC5D,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAa,MAAM;QACjB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEQ,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,WAAW;QAClB,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;IACtC,CAAC;IAEQ,KAAK,CAAC,aAAa;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,mBAAmB;QAC1B,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAEQ,sBAAsB,CAC7B,cAAiD;QAEjD,yDAAyD;QACzD,KAAK,cAAc,EAAE,CAAC;IACxB,CAAC;IAEQ,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEQ,wBAAwB;QAC/B,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,QAAQ,CAAC,aAAuC,EAAE;QACzD,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,kBAAkB;QACzB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,gBAAgB;QACvB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,wBAAwB;QAC/B,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,4BAA4B;QACnC,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,qBAAqB;QAC5B,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,KAAK;QACZ,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,OAAO,CACd,SAAsC,EACtC,SAAkB;QAElB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,OAAO;QACd,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AA9ID,0CA8IC"}