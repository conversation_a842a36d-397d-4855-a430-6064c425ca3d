{"version": 3, "file": "Frame.js", "sourceRoot": "", "sources": ["../../../../src/cdp/Frame.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,8CAAmE;AAGnE,mDAAyD;AACzD,qDAA6C;AAC7C,yDAAoD;AACpD,uDAAiD;AAOjD,yDAAiD;AACjD,2DAAgE;AAChE,+DAG+B;AAG/B;;GAEG;IACU,QAAQ;sBAAS,gBAAK;;;;;;iBAAtB,QAAS,SAAQ,WAAK;;;YAqFjC,+JAAe,IAAI,6DAiFlB;YAGD,sMAAe,iBAAiB,6DA6B/B;YAeD,iLAAe,UAAU,6DA8BxB;YAwBD,4MAAe,mBAAmB,6DAMjC;;;QAhRD,IAAI,yDAAG,EAAE,EAAC;QACV,SAAS,GAAG,KAAK,CAAC;QAClB,OAAO,CAAc;QAErB,aAAa,CAAe;QACnB,GAAG,CAAS;QACrB,SAAS,GAAG,EAAE,CAAC;QACf,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QAC5B,SAAS,CAAU;QAE5B,YACE,YAA0B,EAC1B,OAAe,EACf,aAAiC,EACjC,MAAkB;YAElB,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;YAC/B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAEvB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YAEpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAE1B,IAAI,CAAC,EAAE,CAAC,qBAAU,CAAC,wBAAwB,EAAE,GAAG,EAAE;gBAChD,8CAA8C;gBAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;;;WAIG;QACH,OAAO;YACL,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED;;;WAGG;QACH,QAAQ,CAAC,EAAU;YACjB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QAChB,CAAC;QAED,YAAY,CAAC,MAAkB,EAAE,UAAU,GAAG,KAAK;YACjD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,0DAA0D;gBAC1D,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,YAAY,EAAE,CAAC;oBACvC,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,YAAY,EAAE,CAAC;gBAC9C,CAAC;gBACD,IAAI,CAAC,MAAM,GAAG;oBACZ,CAAC,8BAAU,CAAC,EAAE,IAAI,gCAAa,CAC7B,IAAI,EACJ,IAAI,CAAC,aAAa,CAAC,eAAe,CACnC;oBACD,CAAC,mCAAe,CAAC,EAAE,IAAI,gCAAa,CAClC,IAAI,EACJ,IAAI,CAAC,aAAa,CAAC,eAAe,CACnC;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,YAAY,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,YAAY,EAAE,CAAC;YAC9C,CAAC;QACH,CAAC;QAEQ,IAAI;YACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;QAEQ,UAAU;YACjB,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QACpD,CAAC;QAGQ,KAAK,CAAC,IAAI,CACjB,GAAW,EACX,UAKI,EAAE;YAEN,MAAM,EACJ,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,EACzE,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CACnE,gBAAgB,CACjB,EACD,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;YAEZ,IAAI,2BAA2B,GAAG,KAAK,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;YACF,IAAI,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;gBAC9B,QAAQ,CACN,IAAI,CAAC,OAAO,EACZ,GAAG,EACH,OAAO,EACP,cAA8C,EAC9C,IAAI,CAAC,GAAG,CACT;gBACD,OAAO,CAAC,kBAAkB,EAAE;aAC7B,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;oBAC1B,OAAO,CAAC,kBAAkB,EAAE;oBAC5B,2BAA2B;wBACzB,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE;wBACxC,CAAC,CAAC,OAAO,CAAC,6BAA6B,EAAE;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC;gBACH,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC5C,CAAC;oBAAS,CAAC;gBACT,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;YAED,KAAK,UAAU,QAAQ,CACrB,MAAkB,EAClB,GAAW,EACX,QAA4B,EAC5B,cAAwD,EACxD,OAAe;gBAEf,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;wBAClD,GAAG;wBACH,QAAQ;wBACR,OAAO;wBACP,cAAc;qBACf,CAAC,CAAC;oBACH,2BAA2B,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAClD,IAAI,QAAQ,CAAC,SAAS,KAAK,qCAAqC,EAAE,CAAC;wBACjE,OAAO,IAAI,CAAC;oBACd,CAAC;oBACD,OAAO,QAAQ,CAAC,SAAS;wBACvB,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,OAAO,GAAG,EAAE,CAAC;wBAC9C,CAAC,CAAC,IAAI,CAAC;gBACX,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,EAAE,CAAC;wBACvB,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAGQ,KAAK,CAAC,iBAAiB,CAC9B,UAGI,EAAE;YAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;YACZ,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;YACF,MAAM,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;gBAChC,OAAO,CAAC,kBAAkB,EAAE;gBAC5B,OAAO,CAAC,6BAA6B,EAAE;gBACvC,OAAO,CAAC,4BAA4B,EAAE;aACvC,CAAC,CAAC;YACH,IAAI,CAAC;gBACH,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC5C,CAAC;oBAAS,CAAC;gBACT,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QAED,IAAa,MAAM;YACjB,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAEQ,SAAS;YAChB,OAAO,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC;QACjC,CAAC;QAEQ,aAAa;YACpB,OAAO,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC;QACtC,CAAC;QAGQ,KAAK,CAAC,UAAU,CACvB,IAAY,EACZ,UAGI,EAAE;YAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;YAEZ,oFAAoF;YACpF,iDAAiD;YACjD,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;YACF,MAAM,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAA2B;gBAC1D,OAAO,CAAC,kBAAkB,EAAE;gBAC5B,OAAO,CAAC,gBAAgB,EAAE;aAC3B,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAEQ,GAAG;YACV,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;QACrE,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7D,CAAC;QAED,2BAA2B;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;YAC1C,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QAGQ,KAAK,CAAC,mBAAmB,CAChC,UAA8B,EAAE;YAEhC,OAAO,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC,mBAAmB,CACjE,OAAO,CACR,CAAC;QACJ,CAAC;QAED,UAAU,CAAC,YAAiC;YAC1C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;QACrE,CAAC;QAED,wBAAwB,CAAC,GAAW;YAClC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAClB,CAAC;QAED,iBAAiB,CAAC,QAAgB,EAAE,IAAY;YAC9C,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;QAED,iBAAiB;YACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAC9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,iBAAiB;YACf,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAa,QAAQ;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAED,sBA7NC,0BAAe,oCAoFf,0BAAe,6BA4Cf,0BAAe,sCAsDf,0BAAe,GAuCf,6BAAa,EAAC;YACb,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,6BAAa,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,6BAAa,CAAC,EAAE,CAAC;QAChD,CAAC;QAED,cAAc;YACZ,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;;;AA5TU,4BAAQ"}