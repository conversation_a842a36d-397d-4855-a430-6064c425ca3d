{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/api/HTTPRequest.ts"], "names": [], "mappings": ";;;AAsDA;;;;GAIG;AACU,QAAA,qCAAqC,GAAG,CAAC,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAsB,WAAW;IAC/B;;OAEG;IACH,UAAU,GAAG,EAAE,CAAC;IAChB;;OAEG;IACH,eAAe,CAAqB;IACpC;;OAEG;IACH,YAAY,GAAkB,IAAI,CAAC;IACnC;;OAEG;IACH,SAAS,GAAwB,IAAI,CAAC;IACtC;;OAEG;IACH,gBAAgB,GAAG,KAAK,CAAC;IACzB;;OAEG;IACH,cAAc,GAAkB,EAAE,CAAC;IASnC;;OAEG;IACH,gBAAe,CAAC;CA6PjB;AAjSD,kCAiSC;AAED;;GAEG;AACH,IAAY,yBAOX;AAPD,WAAY,yBAAyB;IACnC,4CAAe,CAAA;IACf,gDAAmB,CAAA;IACnB,kDAAqB,CAAA;IACrB,kDAAqB,CAAA;IACrB,0CAAa,CAAA;IACb,+DAAkC,CAAA;AACpC,CAAC,EAPW,yBAAyB,yCAAzB,yBAAyB,QAOpC;AAiCD;;GAEG;AACH,SAAgB,YAAY,CAC1B,OAA0C;IAE1C,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,CAAC,IAAI,CACT,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACpB,OAAO,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAC,CAAC;YACnC,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAlBD,oCAkBC;AAED;;;;;;GAMG;AACU,QAAA,YAAY,GAA2B;IAClD,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,YAAY;IACnB,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,YAAY;IACnB,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,gBAAgB;IACvB,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,wBAAwB;IAC/B,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,sBAAsB;IAC7B,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,iCAAiC;IACxC,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,4BAA4B;IACnC,KAAK,EAAE,yBAAyB;IAChC,KAAK,EAAE,sBAAsB;IAC7B,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,iCAAiC;CAChC,CAAC"}