#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/esm/puppeteer/node/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/esm/puppeteer/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/esm/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/esm/puppeteer/node/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/esm/puppeteer/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/esm/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/lib/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules/puppeteer/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/puppeteer@21.11.0/node_modules:/Volumes/Work/TrenLop/Crawl-tt/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../puppeteer/lib/esm/puppeteer/node/cli.js" "$@"
else
  exec node  "$basedir/../puppeteer/lib/esm/puppeteer/node/cli.js" "$@"
fi
