hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@puppeteer/browsers@1.9.1':
    '@puppeteer/browsers': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@types/node@22.15.21':
    '@types/node': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  agent-base@7.1.3:
    agent-base: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  ast-types@0.13.4:
    ast-types: private
  b4a@1.6.7:
    b4a: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer@5.7.1:
    buffer: private
  callsites@3.1.0:
    callsites: private
  chokidar@3.6.0:
    chokidar: private
  chromium-bidi@0.5.8(devtools-protocol@0.0.1232444):
    chromium-bidi: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  cosmiconfig@9.0.0:
    cosmiconfig: private
  cross-fetch@4.0.0:
    cross-fetch: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  degenerator@5.0.1:
    degenerator: private
  devtools-protocol@0.0.1232444:
    devtools-protocol: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.4:
    end-of-stream: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  escalade@3.2.0:
    escalade: private
  escodegen@2.1.0:
    escodegen: private
  esprima@4.0.1:
    esprima: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fill-range@7.1.1:
    fill-range: private
  fsevents@2.3.3:
    fsevents: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-stream@5.2.0:
    get-stream: private
  get-uri@6.0.4:
    get-uri: private
  glob-parent@5.1.2:
    glob-parent: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@3.0.0:
    has-flag: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  ieee754@1.2.1:
    ieee754: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  import-fresh@3.3.1:
    import-fresh: private
  inherits@2.0.3:
    inherits: private
  ip-address@9.0.5:
    ip-address: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  jsonfile@6.1.0:
    jsonfile: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  lru-cache@7.18.3:
    lru-cache: private
  minimatch@3.1.2:
    minimatch: private
  mitt@3.0.1:
    mitt: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  ms@2.1.3:
    ms: private
  netmask@2.0.2:
    netmask: private
  node-fetch@2.7.0:
    node-fetch: private
  normalize-path@3.0.0:
    normalize-path: private
  once@1.4.0:
    once: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  process@0.11.10:
    process: private
  progress@2.0.3:
    progress: private
  proxy-agent@6.3.1:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pstree.remy@1.1.8:
    pstree.remy: private
  pump@3.0.2:
    pump: private
  puppeteer-core@21.11.0:
    puppeteer-core: private
  readdirp@3.6.0:
    readdirp: private
  require-directory@2.1.1:
    require-directory: private
  resolve-from@4.0.0:
    resolve-from: private
  semver@7.7.2:
    semver: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  source-map@0.6.1:
    source-map: private
  sprintf-js@1.1.3:
    sprintf-js: private
  streamx@2.22.0:
    streamx: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@7.2.0:
    supports-color: private
  tar-fs@3.0.4:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  text-decoder@1.2.3:
    text-decoder: private
  through@2.3.8:
    through: private
  to-regex-range@5.0.1:
    to-regex-range: private
  touch@3.1.1:
    touch: private
  tr46@0.0.3:
    tr46: private
  tslib@2.8.1:
    tslib: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  universalify@2.0.1:
    universalify: private
  urlpattern-polyfill@10.0.0:
    urlpattern-polyfill: private
  util@0.10.4:
    util: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.16.0:
    ws: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
ignoredBuilds:
  - puppeteer
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.2.1
pendingBuilds: []
prunedAt: Mon, 26 May 2025 01:07:47 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Volumes/Work/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
